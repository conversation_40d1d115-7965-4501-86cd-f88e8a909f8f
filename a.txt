commit 29bdcb6e7dc1252f4eed4b3c24a7df67f96541d9
Author: fang<PERSON><PERSON> <<EMAIL>>
Date:   Sun Jul 27 01:43:59 2025 +0800

    enhance the reconnection mechanism of uiautomator2
    
    Signed-off-by: fang<PERSON><PERSON> <<EMAIL>>

diff --git a/main.py b/main.py
index 63195c9..88d0a8a 100644
--- a/main.py
+++ b/main.py
@@ -614,8 +614,9 @@ class Mitv_Test():
 
         if "#" not in line:
             guardui_stop = False
-            thread_guardUI = threading.Thread(target=self.guard_install_uiautomator,args=(self.device_id, 300, 600, lambda: guardui_stop))
-            thread_guardUI.start()
+            if "RandomPlayerTest" not in class_name:
+                thread_guardUI = threading.Thread(target=self.guard_install_uiautomator,args=(self.device_id, 300, 600, lambda: guardui_stop))
+                thread_guardUI.start()
 
             with open(self.caselog,"a+",encoding="utf-8") as caselog:
                 runner = unittest.TextTestRunner(stream=caselog,verbosity=2,failfast=True)    # case执行结果写入文件
@@ -624,11 +625,11 @@ class Mitv_Test():
                 print(case_result)
                 print("=+" * 30)
 
-            if thread_guardUI.is_alive():
-                guardui_stop = True
-                thread_guardUI.join()  # 让监控uiautomator进程结束
-
-            self.basic_adb.kill_uiautomator()
+            if "RandomPlayerTest" not in class_name:
+                if thread_guardUI.is_alive():
+                    guardui_stop = True
+                    thread_guardUI.join()  # 让监控uiautomator进程结束
+                self.basic_adb.kill_uiautomator()
 
         else:      # 海外的电视小loop在这里
             print("run mitv case {}".format(line))
diff --git a/script/testcases/randomVideotest.py b/script/testcases/randomVideotest.py
index 486a768..0e42d54 100644
--- a/script/testcases/randomVideotest.py
+++ b/script/testcases/randomVideotest.py
@@ -149,11 +149,69 @@ class TVApps:
 
         return TVApps.get_app_name(app_type, manufacturer)
 
-d = u2.connect()  # 连接设备
+# 全局设备连接变量
+d = None
+
+def init_device():
+    """初始化设备连接"""
+    global d
+    try:
+        # 先清理可能存在的UIAutomator2服务冲突
+        cleanup_uiautomator()
+
+        # 连接设备
+        d = u2.connect()
+        d.screen_on()
+        print("设备连接成功")
+        return True
+    except Exception as e:
+        print(f"设备连接失败: {e}")
+        return False
+
+# 尝试初始化设备连接
+if not init_device():
+    print("警告: 设备初始化失败，测试可能不稳定")
+
+def cleanup_uiautomator():
+    """彻底清理UIAutomator2服务"""
+    try:
+        print("开始清理UIAutomator2服务...")
+        # 停止所有相关进程
+        timeout_command('adb shell am force-stop com.github.uiautomator')
+        timeout_command('adb shell am force-stop com.github.uiautomator.test')
+        timeout_command('adb shell killall uiautomator')
+        timeout_command('adb shell killall com.github.uiautomator')
+
+        # 清理accessibility服务
+        timeout_command('adb shell settings put secure enabled_accessibility_services ""')
+        time.sleep(2)
+
+        # 重启atx-agent
+        timeout_command('adb shell pkill atx-agent')
+        time.sleep(3)
+        timeout_command('adb shell /data/local/tmp/atx-agent server -d --stop')
+        time.sleep(2)
+        timeout_command('adb shell /data/local/tmp/atx-agent server -d')
+        time.sleep(5)
+
+        print("UIAutomator2服务清理完成")
+        return True
+    except Exception as e:
+        print(f"清理UIAutomator2服务失败: {e}")
+        return False
 
 def setup_watchers():
     """设置UIAutomator2 watchers"""
     try:
+        # 先停止并清理现有的watchers
+        try:
+            d.watcher.stop()
+            d.watcher.remove()
+            time.sleep(1)
+        except:
+            print("停止watchers时出错")
+            pass
+
         d.watcher.when("升级").click()
         d.watcher.when("完成").click()
         d.watcher.when("安装").click()
@@ -183,8 +241,38 @@ def setup_watchers():
         # 启动 watchers
         d.watcher.start()
         print("watchers 设置完成")
+        return True
     except Exception as e:
         print(f"watchers 设置失败: {e}")
+        return False
+
+def reconnect_device(max_retries=3):
+    """重连设备并重新初始化"""
+    global d
+    for i in range(max_retries):
+        try:
+            print(f"尝试重连设备 ({i+1}/{max_retries})...")
+
+            # 彻底清理UIAutomator2服务
+            cleanup_uiautomator()
+
+            # 重新连接
+            d = u2.connect()
+            d.screen_on()
+
+            # 重新设置watchers
+            if setup_watchers():
+                print("设备重连成功")
+                return True
+            else:
+                print("watchers设置失败，继续重试...")
+
+        except Exception as e:
+            print(f"重连尝试 {i+1} 失败: {e}")
+            time.sleep(5)
+
+    print("设备重连失败，所有重试都已用完")
+    return False
 
 def ui_safe(func):
     """UIAutomator2安全装饰器"""
@@ -193,15 +281,16 @@ def ui_safe(func):
             return func(self, *args, **kwargs)
         except Exception as e:
             print(f'{func.__name__} UIAutomator2异常: {e}')
-            try:
-                global d
-                d = u2.connect()
-                print(f'{func.__name__} 重连成功，重新设置watchers...')
-                setup_watchers()  # 重新设置watchers
-                d.screen_on()  # 唤醒屏幕
-                return func(self, *args, **kwargs)
-            except Exception as reconnect_error:
-                print(f'{func.__name__} 重连后失败: {reconnect_error}')
+
+            # 尝试重连设备
+            if reconnect_device():
+                try:
+                    return func(self, *args, **kwargs)
+                except Exception as retry_error:
+                    print(f'{func.__name__} 重连后仍然失败: {retry_error}')
+                    return False
+            else:
+                print(f'{func.__name__} 设备重连失败')
                 return False
     return wrapper
 
@@ -211,12 +300,19 @@ class RandomPlayerTest(unittest.TestCase):
         called before  each test method start.
         """
         try:
-            # 使用统一的watchers设置函数
-            setup_watchers()
+            # 先尝试正常设置watchers
+            if not setup_watchers():
+                print("watchers设置失败，尝试重连设备...")
+                if not reconnect_device():
+                    print("设备重连失败，测试可能不稳定")
+
             d.screen_on()  # 唤醒屏幕
             time.sleep(10)
         except Exception as e:
             print(f"初始化出错: {e}")
+            # 尝试重连设备
+            if not reconnect_device():
+                print("初始化失败，设备连接异常")
 
         back2home_page()
     def tearDown(self):
@@ -226,10 +322,14 @@ class RandomPlayerTest(unittest.TestCase):
         try:
             d.watcher.stop()  # 停止所有watchers
             d.watcher.remove()  # 移除所有watchers
+            time.sleep(1)  # 给一点时间让watchers完全停止
         except Exception as e:
             print(f"停止watchers时出错: {e}")
-        # d.press("home")
-        back2home_page()
+
+        try:
+            back2home_page()
+        except Exception as e:
+            print(f"返回主页时出错: {e}")
 
     def video_interaction_operations(self):
         """
